import React, { memo, PureComponent } from 'react';
import { Form, Input, Radio, Select, Checkbox, DatePicker } from 'antd';
import TextAreaHoc from './TextAreaHoc';
import InputHoc from './InputHoc';
import styles from './index.less';
import moment from 'moment';

const { RangePicker } = DatePicker;

class FormInput extends PureComponent {
  render() {
    return (
      <InputHoc {...this.props}>
        <Input size="small" spellCheck={false}></Input>
      </InputHoc>
    );
  }
}

class FormTextArea extends PureComponent {
  render() {
    return (
      <TextAreaHoc {...this.props}>
        <Input.TextArea size="small" autoSize spellCheck={false}></Input.TextArea>
      </TextAreaHoc>
    );
  }
}

class FormRadio extends PureComponent {
  render() {
    const { options = [], ...rest } = this.props;
    return (
      <Radio.Group {...rest}>
        {options.map(v => (
          <Radio value={v.value} key={v.value}>
            {v.label}
          </Radio>
        ))}
      </Radio.Group>
    );
  }
}

class FormCheckBox extends PureComponent {
  render() {
    const { options = [], ...rest } = this.props;
    return (
      <Checkbox.Group {...rest}>
        {options.map(v => (
          <Checkbox value={v.value} key={v.value}>
            {v.label}
          </Checkbox>
        ))}
      </Checkbox.Group>
    );
  }
}

class FormSelect extends PureComponent {
  render() {
    const { options = [], ...rest } = this.props;
    return (
      <Select {...rest}>
        {options.map(v => (
          <Select.Option value={v.value} key={v.value}>
            {v.label}
          </Select.Option>
        ))}
      </Select>
    );
  }
}

class FormRangePicker extends PureComponent {
  render() {
    const { ...rest } = this.props;

    // 限制最长不能超过5天的校验函数
    const disabledDate = (current, { from }) => {
      if (!from) {
        return false;
      }
      const tooLate = current.diff(from, 'days') >= 5;
      const tooEarly = from.diff(current, 'days') >= 5;
      return tooEarly || tooLate;
    };

    return (
      <RangePicker
        {...rest}
        format="YYYY-MM-DD HH:mm"
        showTime={{
          format: 'HH:mm',
          defaultValue: [moment('00:00', 'HH:mm'), moment('23:59', 'HH:mm')]
        }}
        disabledDate={disabledDate}
        placeholder={['开始时间', '结束时间']}
      />
    );
  }
}

const formControllMap = {
  input: FormInput,
  textarea: FormTextArea,
  radio: FormRadio,
  select: FormSelect,
  checkBox: FormCheckBox,
  rangePicker: FormRangePicker,
};

const FormItem = memo(props => {
  const {
    type,
    form,
    field,
    initialValue,
    required = true,
    label = '',
    formItemLayout = {},
    style = {},
    formItemClassName = '',
    rules = [],
    ...rest
  } = props;
  const { getFieldDecorator } = form;

  const FormControll = formControllMap[type];
  return (
    <Form.Item label={label} {...formItemLayout} className={styles[formItemClassName]}>
      {getFieldDecorator(field, {
        initialValue: initialValue,
        rules: [{ required: required, message: '必填' }, ...rules],
      })(<FormControll {...rest} style={style}></FormControll>)}
    </Form.Item>
  );
});

export { FormInput, FormTextArea, FormRadio, FormItem };
