import React, { memo, PureComponent } from 'react';
import { Form, Input, Radio, Select, Checkbox, DatePicker } from 'antd';
import TextAreaHoc from './TextAreaHoc';
import InputHoc from './InputHoc';
import styles from './index.less';
import moment from 'moment';

const { RangePicker } = DatePicker;

class FormInput extends PureComponent {
  render() {
    return (
      <InputHoc {...this.props}>
        <Input size="small" spellCheck={false}></Input>
      </InputHoc>
    );
  }
}

class FormTextArea extends PureComponent {
  render() {
    return (
      <TextAreaHoc {...this.props}>
        <Input.TextArea size="small" autoSize spellCheck={false}></Input.TextArea>
      </TextAreaHoc>
    );
  }
}

class FormRadio extends PureComponent {
  render() {
    const { options = [], ...rest } = this.props;
    return (
      <Radio.Group {...rest}>
        {options.map(v => (
          <Radio value={v.value} key={v.value}>
            {v.label}
          </Radio>
        ))}
      </Radio.Group>
    );
  }
}

class FormCheckBox extends PureComponent {
  render() {
    const { options = [], ...rest } = this.props;
    return (
      <Checkbox.Group {...rest}>
        {options.map(v => (
          <Checkbox value={v.value} key={v.value}>
            {v.label}
          </Checkbox>
        ))}
      </Checkbox.Group>
    );
  }
}

class FormSelect extends PureComponent {
  render() {
    const { options = [], ...rest } = this.props;
    return (
      <Select {...rest}>
        {options.map(v => (
          <Select.Option value={v.value} key={v.value}>
            {v.label}
          </Select.Option>
        ))}
      </Select>
    );
  }
}

class FormRangePicker extends PureComponent {
  // 验证选择的时间范围是否超过5天
  validateRange = (dates) => {
    if (!dates || dates.length !== 2 || !dates[0] || !dates[1]) {
      return true;
    }

    const [start, end] = dates;
    const diffInDays = end.diff(start, 'days');
    const diffInHours = end.diff(start, 'hours');

    // 如果超过5天（120小时），返回false表示无效
    if (diffInDays > 5 || (diffInDays === 5 && diffInHours > 120)) {
      return false;
    }

    return true;
  };

  handleChange = (dates, dateStrings) => {
    const { onChange } = this.props;

    // 验证时间范围
    if (dates && dates.length === 2 && !this.validateRange(dates)) {
      // 如果超过5天范围，显示警告并清空选择
      console.warn('选择的时间范围不能超过5天');
      return;
    }

    if (onChange) {
      onChange(dates, dateStrings);
    }
  };

  render() {
    const { onChange, ...rest } = this.props;

    return (
      <RangePicker
        {...rest}
        format="YYYY-MM-DD HH:mm"
        showTime={{
          format: 'HH:mm',
          defaultValue: [moment('00:00', 'HH:mm'), moment('23:59', 'HH:mm')],
          hideDisabledOptions: true
        }}
        disabledDate={(current, { from }) => {
          if (!from) {
            return false;
          }
          // 限制范围不能超过5天
          const diffInDays = Math.abs(current.diff(from, 'days'));
          return diffInDays > 5;
        }}
        placeholder={['开始时间', '结束时间']}
        onChange={this.handleChange}
        onOk={this.handleChange}
      />
    );
  }
}

const formControllMap = {
  input: FormInput,
  textarea: FormTextArea,
  radio: FormRadio,
  select: FormSelect,
  checkBox: FormCheckBox,
  rangePicker: FormRangePicker,
};

const FormItem = memo(props => {
  const {
    type,
    form,
    field,
    initialValue,
    required = true,
    label = '',
    formItemLayout = {},
    style = {},
    formItemClassName = '',
    rules = [],
    ...rest
  } = props;
  const { getFieldDecorator } = form;

  const FormControll = formControllMap[type];
  return (
    <Form.Item label={label} {...formItemLayout} className={styles[formItemClassName]}>
      {getFieldDecorator(field, {
        initialValue: initialValue,
        rules: [{ required: required, message: '必填' }, ...rules],
      })(<FormControll {...rest} style={style}></FormControll>)}
    </Form.Item>
  );
});

export { FormInput, FormTextArea, FormRadio, FormItem };
