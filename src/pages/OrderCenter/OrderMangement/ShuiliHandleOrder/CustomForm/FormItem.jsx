import React, { memo, PureComponent } from 'react';
import { Form, Input, Radio, Select, Checkbox, DatePicker } from 'antd';
import TextAreaHoc from './TextAreaHoc';
import InputHoc from './InputHoc';
import styles from './index.less';
import moment from 'moment';

const { RangePicker } = DatePicker;

class FormInput extends PureComponent {
  render() {
    return (
      <InputHoc {...this.props}>
        <Input size="small" spellCheck={false}></Input>
      </InputHoc>
    );
  }
}

class FormTextArea extends PureComponent {
  render() {
    return (
      <TextAreaHoc {...this.props}>
        <Input.TextArea size="small" autoSize spellCheck={false}></Input.TextArea>
      </TextAreaHoc>
    );
  }
}

class FormRadio extends PureComponent {
  render() {
    const { options = [], ...rest } = this.props;
    return (
      <Radio.Group {...rest}>
        {options.map(v => (
          <Radio value={v.value} key={v.value}>
            {v.label}
          </Radio>
        ))}
      </Radio.Group>
    );
  }
}

class FormCheckBox extends PureComponent {
  render() {
    const { options = [], ...rest } = this.props;
    return (
      <Checkbox.Group {...rest}>
        {options.map(v => (
          <Checkbox value={v.value} key={v.value}>
            {v.label}
          </Checkbox>
        ))}
      </Checkbox.Group>
    );
  }
}

class FormSelect extends PureComponent {
  render() {
    const { options = [], ...rest } = this.props;
    return (
      <Select {...rest}>
        {options.map(v => (
          <Select.Option value={v.value} key={v.value}>
            {v.label}
          </Select.Option>
        ))}
      </Select>
    );
  }
}

class FormRangePicker extends PureComponent {
  state = {
    startValue: null,
  };

  // 禁用日期函数 - 基于已选择的开始时间限制结束时间
  disabledDate = (current) => {
    const { startValue } = this.state;

    if (startValue && current) {
      // 计算与开始时间的差值（天数）
      const diffInDays = Math.abs(current.diff(startValue, 'days'));
      // 如果超过5天，禁用该日期
      return diffInDays > 5;
    }

    return false;
  };

  // 日历面板变化时的回调
  onCalendarChange = (dates) => {
    if (dates && dates.length === 1) {
      // 当选择了开始时间时，记录开始时间用于限制结束时间
      this.setState({ startValue: dates[0] });
    } else {
      // 清空或选择完成时重置
      this.setState({ startValue: null });
    }
  };

  // 面板打开状态变化时重置状态
  onOpenChange = (open) => {
    if (!open) {
      this.setState({ startValue: null });
    }
  };

  // 验证选择的时间范围是否超过5天
  validateRange = (dates) => {
    if (!dates || dates.length !== 2 || !dates[0] || !dates[1]) {
      return true;
    }

    const [start, end] = dates;
    const diffInHours = end.diff(start, 'hours');

    // 如果超过5天（120小时），返回false表示无效
    return diffInHours <= 120;
  };

  handleChange = (dates, dateStrings) => {
    const { onChange } = this.props;

    // 验证时间范围
    if (dates && dates.length === 2 && !this.validateRange(dates)) {
      console.warn('选择的时间范围不能超过5天');
      return;
    }

    if (onChange) {
      onChange(dates, dateStrings);
    }
  };

  render() {
    const { onChange, ...rest } = this.props;

    return (
      <RangePicker
        {...rest}
        format="YYYY-MM-DD HH:mm"
        showTime={{
          format: 'HH:mm',
          defaultValue: [moment('00:00', 'HH:mm'), moment('23:59', 'HH:mm')]
        }}
        disabledDate={this.disabledDate}
        onCalendarChange={this.onCalendarChange}
        onOpenChange={this.onOpenChange}
        placeholder={['开始时间', '结束时间']}
        onChange={this.handleChange}
        onOk={this.handleChange}
      />
    );
  }
}

const formControllMap = {
  input: FormInput,
  textarea: FormTextArea,
  radio: FormRadio,
  select: FormSelect,
  checkBox: FormCheckBox,
  rangePicker: FormRangePicker,
};

const FormItem = memo(props => {
  const {
    type,
    form,
    field,
    initialValue,
    required = true,
    label = '',
    formItemLayout = {},
    style = {},
    formItemClassName = '',
    rules = [],
    ...rest
  } = props;
  const { getFieldDecorator } = form;

  const FormControll = formControllMap[type];
  return (
    <Form.Item label={label} {...formItemLayout} className={styles[formItemClassName]}>
      {getFieldDecorator(field, {
        initialValue: initialValue,
        rules: [{ required: required, message: '必填' }, ...rules],
      })(<FormControll {...rest} style={style}></FormControll>)}
    </Form.Item>
  );
});

export { FormInput, FormTextArea, FormRadio, FormItem };
