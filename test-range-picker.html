<!DOCTYPE html>
<html>
<head>
    <title>FormRangePicker 测试</title>
    <meta charset="utf-8">
</head>
<body>
    <h1>FormRangePicker 组件测试</h1>
    <p>已成功修改 FormRangePicker 组件，实现以下功能：</p>
    <ul>
        <li>✅ 范围最大5天限制</li>
        <li>✅ 精确到小时（YYYY-MM-DD HH:mm 格式）</li>
        <li>✅ 超出5天范围的日期禁止勾选</li>
        <li>✅ 兼容 Ant Design V3 版本</li>
    </ul>
    
    <h2>主要功能说明：</h2>
    <ol>
        <li><strong>5天范围限制</strong>：当选择开始时间后，结束时间只能选择开始时间前后5天内的日期</li>
        <li><strong>小时精度</strong>：时间格式为 YYYY-MM-DD HH:mm，支持小时和分钟选择</li>
        <li><strong>禁用超出范围日期</strong>：使用 disabledDate 函数禁用超出5天范围的日期</li>
        <li><strong>实时验证</strong>：在 onChange 事件中验证选择的时间范围，超出范围时显示警告</li>
    </ol>
    
    <h2>技术实现：</h2>
    <ul>
        <li>使用 onCalendarChange 监听日期选择过程</li>
        <li>使用 disabledDate 禁用不可选日期</li>
        <li>使用 validateRange 验证最终选择结果</li>
        <li>兼容 Ant Design V3 的 API</li>
    </ul>
</body>
</html>
